"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const fs_1 = require("fs");
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const DatabaseService_1 = require("./services/DatabaseService");
const FileService_1 = require("./services/FileService");
const GoogleDriveService_1 = require("./services/GoogleDriveService");
const common_1 = require("../types/common");
class ElectronApp {
    constructor() {
        this.mainWindow = null;
        this.databaseService = null;
        this.fileService = null;
        this.googleDriveService = null;
        this.initializeApp();
    }
    async initializeApp() {
        // Handle app events
        electron_1.app.whenReady().then(() => {
            this.createWindow();
            this.initializeServices();
            this.setupIpcHandlers();
        });
        electron_1.app.on("window-all-closed", () => {
            if (process.platform !== "darwin") {
                electron_1.app.quit();
            }
        });
        electron_1.app.on("activate", () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
    }
    createWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, "preload.js"),
            },
            icon: (0, path_1.join)(__dirname, "../../public/icon.ico"),
            show: false, // Don't show until ready
            titleBarStyle: "default",
        });
        // Load the app
        if (process.env.NODE_ENV === "development") {
            this.mainWindow.loadURL("http://localhost:3000");
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, "../renderer/index.html"));
        }
        // Show window when ready
        this.mainWindow.once("ready-to-show", () => {
            this.mainWindow?.show();
        });
        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            electron_1.shell.openExternal(url);
            return { action: "deny" };
        });
    }
    async initializeServices() {
        try {
            // Ensure data directories exist
            const userDataPath = electron_1.app.getPath("userData");
            const dbPath = (0, path_1.join)(userDataPath, "database");
            const filesPath = (0, path_1.join)(userDataPath, "files");
            if (!(0, fs_1.existsSync)(dbPath))
                (0, fs_1.mkdirSync)(dbPath, { recursive: true });
            if (!(0, fs_1.existsSync)(filesPath))
                (0, fs_1.mkdirSync)(filesPath, { recursive: true });
            // Initialize database
            const dbFile = (0, path_1.join)(dbPath, "apartment_rental.db");
            const database = new better_sqlite3_1.default(dbFile);
            this.databaseService = new DatabaseService_1.DatabaseService(database);
            await this.databaseService.initialize();
            // Initialize file service
            this.fileService = new FileService_1.FileService(filesPath);
            // Initialize Google Drive service
            this.googleDriveService = new GoogleDriveService_1.GoogleDriveService();
            console.log("All services initialized successfully");
        }
        catch (error) {
            console.error("Failed to initialize services:", error);
            electron_1.dialog.showErrorBox("Initialization Error", "Failed to initialize application services. Please restart the application.");
        }
    }
    setupIpcHandlers() {
        // Database operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_INIT, async () => {
            return this.databaseService?.initialize();
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_QUERY, async (_, sql, params) => {
            return this.databaseService?.query(sql, params);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_EXECUTE, async (_, sql, params) => {
            return this.databaseService?.execute(sql, params);
        });
        // File operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_UPLOAD, async (_, fileData, fileName, entityType, entityId, fieldName) => {
            return this.fileService?.uploadFile(fileData, fileName, entityType, entityId, fieldName);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_DELETE, async (_, filePath) => {
            return this.fileService?.deleteFile(filePath);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_GENERATE_THUMBNAIL, async (_, filePath) => {
            return this.fileService?.generateThumbnail(filePath);
        });
        // Building operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.BUILDING_CREATE, async (_, data) => {
            try {
                const sql = `
          INSERT INTO buildings (
            name, address, owner_name, owner_mobile_no, owner_address,
            rent_amount, advance, number_of_floor, agreement_date,
            hand_over_from_owner, hand_over_to_owner, conditions, comments
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
                const result = this.databaseService?.execute(sql, [
                    data.name, data.address, data.ownerName, data.ownerMobileNo,
                    data.ownerAddress, data.rentAmount, data.advance, data.numberOfFloor,
                    data.agreementDate, data.handOverFromOwner, data.handOverToOwner,
                    data.conditions, data.comments
                ]);
                return { success: true, data: { id: result?.lastInsertRowid, ...data } };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to create building' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.BUILDING_GET_ALL, async () => {
            try {
                const sql = `SELECT * FROM buildings ORDER BY created_at DESC`;
                const buildings = this.databaseService?.query(sql) || [];
                return { success: true, data: buildings };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch buildings' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.BUILDING_UPDATE, async (_, data) => {
            try {
                const sql = `
          UPDATE buildings SET
            name = ?, address = ?, owner_name = ?, owner_mobile_no = ?,
            owner_address = ?, rent_amount = ?, advance = ?, number_of_floor = ?,
            agreement_date = ?, hand_over_from_owner = ?, hand_over_to_owner = ?,
            conditions = ?, comments = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
                this.databaseService?.execute(sql, [
                    data.name, data.address, data.ownerName, data.ownerMobileNo,
                    data.ownerAddress, data.rentAmount, data.advance, data.numberOfFloor,
                    data.agreementDate, data.handOverFromOwner, data.handOverToOwner,
                    data.conditions, data.comments, data.id
                ]);
                return { success: true, data };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to update building' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.BUILDING_DELETE, async (_, id) => {
            try {
                const sql = `DELETE FROM buildings WHERE id = ?`;
                this.databaseService?.execute(sql, [id]);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to delete building' };
            }
        });
        // Floor operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FLOOR_CREATE, async (_, data) => {
            try {
                const sql = `
          INSERT INTO floors (name, floor_number, building_id)
          VALUES (?, ?, ?)
        `;
                const result = this.databaseService?.execute(sql, [
                    data.name, data.floorNumber, data.buildingId
                ]);
                return { success: true, data: { id: result?.lastInsertRowid, ...data } };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to create floor' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FLOOR_GET_BY_BUILDING, async (_, buildingId) => {
            try {
                const sql = `
          SELECT f.*,
                 COUNT(a.id) as total_apartments,
                 COUNT(CASE WHEN a.current_customer_id IS NOT NULL THEN 1 END) as occupied_apartments,
                 COUNT(CASE WHEN a.current_customer_id IS NULL THEN 1 END) as available_apartments
          FROM floors f
          LEFT JOIN apartments a ON f.id = a.floor_id
          WHERE f.building_id = ?
          GROUP BY f.id
          ORDER BY f.floor_number
        `;
                const floors = this.databaseService?.query(sql, [buildingId]) || [];
                return { success: true, data: floors };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch floors' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FLOOR_UPDATE, async (_, data) => {
            try {
                const sql = `
          UPDATE floors SET name = ?, floor_number = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
                this.databaseService?.execute(sql, [data.name, data.floorNumber, data.id]);
                return { success: true, data };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to update floor' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FLOOR_DELETE, async (_, id) => {
            try {
                const sql = `DELETE FROM floors WHERE id = ?`;
                this.databaseService?.execute(sql, [id]);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to delete floor' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FLOOR_AUTO_GENERATE, async (_, buildingId, numberOfFloors) => {
            try {
                const floors = [];
                for (let i = 0; i < numberOfFloors; i++) {
                    const name = i === 0 ? 'Ground Floor' : `${i}${i === 1 ? 'st' : i === 2 ? 'nd' : i === 3 ? 'rd' : 'th'} Floor`;
                    const sql = `INSERT INTO floors (name, floor_number, building_id) VALUES (?, ?, ?)`;
                    const result = this.databaseService?.execute(sql, [name, i, buildingId]);
                    floors.push({ id: result?.lastInsertRowid, name, floorNumber: i, buildingId });
                }
                return { success: true, data: floors };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to auto-generate floors' };
            }
        });
        // Apartment operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.APARTMENT_CREATE, async (_, data) => {
            try {
                const sql = `
          INSERT INTO apartments (name, current_rate, size, number_of_rooms, floor_id)
          VALUES (?, ?, ?, ?, ?)
        `;
                const result = this.databaseService?.execute(sql, [
                    data.name, data.currentRate, data.size, data.numberOfRooms, data.floorId
                ]);
                return { success: true, data: { id: result?.lastInsertRowid, ...data } };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to create apartment' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.APARTMENT_GET_BY_FLOOR, async (_, floorId) => {
            try {
                const sql = `
          SELECT a.*,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address,
                 c.name as customer_name, c.mobile as customer_mobile,
                 CASE WHEN a.current_customer_id IS NOT NULL THEN 1 ELSE 0 END as is_occupied
          FROM apartments a
          JOIN floors f ON a.floor_id = f.id
          JOIN buildings b ON f.building_id = b.id
          LEFT JOIN customers c ON a.current_customer_id = c.id
          WHERE a.floor_id = ?
          ORDER BY a.name
        `;
                const apartments = this.databaseService?.query(sql, [floorId]) || [];
                return { success: true, data: apartments };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch apartments' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.APARTMENT_UPDATE, async (_, data) => {
            try {
                const sql = `
          UPDATE apartments SET
            name = ?, current_rate = ?, size = ?, number_of_rooms = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
                this.databaseService?.execute(sql, [
                    data.name, data.currentRate, data.size, data.numberOfRooms, data.id
                ]);
                return { success: true, data };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to update apartment' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.APARTMENT_DELETE, async (_, id) => {
            try {
                const sql = `DELETE FROM apartments WHERE id = ?`;
                this.databaseService?.execute(sql, [id]);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to delete apartment' };
            }
        });
        // Customer operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.CUSTOMER_CREATE, async (_, data) => {
            try {
                const sql = `
          INSERT INTO customers (
            name, address, mobile, occupation, start_date, rent, advance,
            nid_path, photo_path, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
                const result = this.databaseService?.execute(sql, [
                    data.name, data.address, data.mobile, data.occupation,
                    data.startDate, data.rent, data.advance, data.nidPath,
                    data.photoPath, data.isActive !== false ? 1 : 0
                ]);
                return { success: true, data: { id: result?.lastInsertRowid, ...data } };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to create customer' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.CUSTOMER_GET_ALL, async () => {
            try {
                const sql = `
          SELECT c.*,
                 a.name as apartment_name, a.size as apartment_size, a.number_of_rooms,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address
          FROM customers c
          LEFT JOIN apartments a ON c.id = a.current_customer_id
          LEFT JOIN floors f ON a.floor_id = f.id
          LEFT JOIN buildings b ON f.building_id = b.id
          ORDER BY c.created_at DESC
        `;
                const customers = this.databaseService?.query(sql) || [];
                return { success: true, data: customers };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch customers' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.CUSTOMER_UPDATE, async (_, data) => {
            try {
                const sql = `
          UPDATE customers SET
            name = ?, address = ?, mobile = ?, occupation = ?,
            start_date = ?, rent = ?, advance = ?, nid_path = ?,
            photo_path = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
                this.databaseService?.execute(sql, [
                    data.name, data.address, data.mobile, data.occupation,
                    data.startDate, data.rent, data.advance, data.nidPath,
                    data.photoPath, data.isActive ? 1 : 0, data.id
                ]);
                return { success: true, data };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to update customer' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.CUSTOMER_DELETE, async (_, id) => {
            try {
                const sql = `DELETE FROM customers WHERE id = ?`;
                this.databaseService?.execute(sql, [id]);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to delete customer' };
            }
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.CUSTOMER_GET_AVAILABLE, async () => {
            try {
                const sql = `
          SELECT * FROM customers
          WHERE is_active = 0 OR id NOT IN (SELECT current_customer_id FROM apartments WHERE current_customer_id IS NOT NULL)
          ORDER BY name
        `;
                const customers = this.databaseService?.query(sql) || [];
                return { success: true, data: customers };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch available customers' };
            }
        });
        // Google Drive operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.GOOGLE_AUTH, async () => {
            return this.googleDriveService?.authenticate();
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.GOOGLE_SYNC, async () => {
            return this.googleDriveService?.syncData();
        });
        // Dialog operations
        electron_1.ipcMain.handle("dialog:openFile", async (_, options) => {
            const result = await electron_1.dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });
        electron_1.ipcMain.handle("dialog:saveFile", async (_, options) => {
            const result = await electron_1.dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });
        electron_1.ipcMain.handle("dialog:showMessage", async (_, options) => {
            const result = await electron_1.dialog.showMessageBox(this.mainWindow, options);
            return result;
        });
    }
}
// Create the application instance
new ElectronApp();
