-- Apartment Rental Management System Database Schema
-- SQLite Database Schema with proper relationships and constraints

-- Users table for Google Drive integration
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    google_access_token TEXT,
    google_refresh_token TEXT,
    last_sync_date D<PERSON>ETIM<PERSON>,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Buildings table
CREATE TABLE IF NOT EXISTS buildings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    owner_name TEXT NOT NULL,
    owner_mobile_no TEXT NOT NULL,
    owner_address TEXT NOT NULL,
    rent_amount DECIMAL(10,2) NOT NULL,
    advance DECIMAL(10,2) NOT NULL,
    number_of_floor INTEGER NOT NULL CHECK(number_of_floor >= 0),
    agreement_date DATE NOT NULL,
    hand_over_from_owner DATE NOT NULL,
    hand_over_to_owner DAT<PERSON>,
    conditions TEXT,
    owner_nid_path TEXT, -- File path for owner NID document
    owner_photo_path TEXT, -- File path for owner photo
    comments TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Floors table (auto-generated based on building's number_of_floor)
CREATE TABLE IF NOT EXISTS floors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL, -- "Ground Floor", "1st Floor", "2nd Floor", etc.
    floor_number INTEGER NOT NULL, -- 0 for ground, 1, 2, 3, etc.
    building_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (building_id) REFERENCES buildings(id) ON DELETE CASCADE,
    UNIQUE(building_id, floor_number)
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    mobile TEXT NOT NULL,
    occupation TEXT NOT NULL,
    start_date DATE NOT NULL,
    rent DECIMAL(10,2) NOT NULL,
    advance DECIMAL(10,2) NOT NULL,
    nid_path TEXT, -- File path for customer NID document
    photo_path TEXT, -- File path for customer photo
    is_active BOOLEAN DEFAULT 1, -- Whether customer is currently renting
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Apartments table
CREATE TABLE IF NOT EXISTS apartments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    current_rate DECIMAL(10,2) NOT NULL,
    size TEXT NOT NULL,
    number_of_rooms INTEGER NOT NULL CHECK(number_of_rooms > 0),
    floor_id INTEGER NOT NULL,
    current_customer_id INTEGER, -- NULL if apartment is available
    is_occupied BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE CASCADE,
    FOREIGN KEY (current_customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Apartment booking history table
CREATE TABLE IF NOT EXISTS apartment_bookings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apartment_id INTEGER NOT NULL,
    customer_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE, -- NULL if currently occupied
    rent_amount DECIMAL(10,2) NOT NULL,
    advance_amount DECIMAL(10,2) NOT NULL,
    checkout_reason TEXT, -- Reason for checkout if applicable
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (apartment_id) REFERENCES apartments(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- File metadata table for tracking uploaded files
CREATE TABLE IF NOT EXISTS file_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL UNIQUE,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    thumbnail_path TEXT, -- For images
    entity_type TEXT NOT NULL, -- 'building', 'customer'
    entity_id INTEGER NOT NULL,
    field_name TEXT NOT NULL, -- 'owner_nid', 'owner_photo', 'nid', 'photo'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_floors_building_id ON floors(building_id);
CREATE INDEX IF NOT EXISTS idx_apartments_floor_id ON apartments(floor_id);
CREATE INDEX IF NOT EXISTS idx_apartments_customer_id ON apartments(current_customer_id);
CREATE INDEX IF NOT EXISTS idx_bookings_apartment_id ON apartment_bookings(apartment_id);
CREATE INDEX IF NOT EXISTS idx_bookings_customer_id ON apartment_bookings(customer_id);
CREATE INDEX IF NOT EXISTS idx_file_metadata_entity ON file_metadata(entity_type, entity_id);

-- Triggers to update timestamps
CREATE TRIGGER IF NOT EXISTS update_buildings_timestamp
    AFTER UPDATE ON buildings
    BEGIN
        UPDATE buildings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_floors_timestamp
    AFTER UPDATE ON floors
    BEGIN
        UPDATE floors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_customers_timestamp
    AFTER UPDATE ON customers
    BEGIN
        UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_apartments_timestamp
    AFTER UPDATE ON apartments
    BEGIN
        UPDATE apartments SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_bookings_timestamp
    AFTER UPDATE ON apartment_bookings
    BEGIN
        UPDATE apartment_bookings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;