import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';

export class DatabaseService {
  private db: Database.Database;

  constructor(database: Database.Database) {
    this.db = database;
  }

  async initialize(): Promise<void> {
    try {
      // Enable foreign keys
      this.db.pragma('foreign_keys = ON');

      // Create tables manually to avoid schema parsing issues
      this.createTables();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private createTables(): void {
    // Create buildings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS buildings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        owner_name TEXT NOT NULL,
        owner_mobile_no TEXT NOT NULL,
        owner_address TEXT NOT NULL,
        rent_amount REAL NOT NULL,
        advance REAL NOT NULL,
        number_of_floor INTEGER NOT NULL,
        agreement_date TEXT NOT NULL,
        hand_over_from_owner TEXT NOT NULL,
        hand_over_to_owner TEXT,
        conditions TEXT,
        comments TEXT,
        owner_photo_path TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create floors table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS floors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        floor_number INTEGER NOT NULL,
        building_id INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (building_id) REFERENCES buildings(id) ON DELETE CASCADE,
        UNIQUE(floor_number, building_id)
      );
    `);

    // Create apartments table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS apartments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        current_rate REAL NOT NULL,
        size TEXT NOT NULL,
        number_of_rooms INTEGER NOT NULL,
        floor_id INTEGER NOT NULL,
        current_customer_id INTEGER,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE CASCADE,
        FOREIGN KEY (current_customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        UNIQUE(name, floor_id)
      );
    `);

    // Create customers table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        mobile TEXT NOT NULL UNIQUE,
        occupation TEXT NOT NULL,
        start_date TEXT NOT NULL,
        rent REAL NOT NULL,
        advance REAL NOT NULL,
        is_active INTEGER DEFAULT 1,
        nid_path TEXT,
        photo_path TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create apartment_bookings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS apartment_bookings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        apartment_id INTEGER NOT NULL,
        customer_id INTEGER NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        rent_amount REAL NOT NULL,
        advance_amount REAL NOT NULL,
        checkout_reason TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (apartment_id) REFERENCES apartments(id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      );
    `);

    // Create files table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_type TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER NOT NULL,
        file_category TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }

  query(sql: string, params: any[] = []): any[] {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.all(params);
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  execute(sql: string, params: any[] = []): Database.RunResult {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.run(params);
    } catch (error) {
      console.error('Database execute error:', error);
      throw error;
    }
  }

  transaction(callback: () => void): void {
    const transaction = this.db.transaction(callback);
    transaction();
  }

  close(): void {
    this.db.close();
  }

  // Helper methods for common operations
  getLastInsertId(): number {
    const result = this.query('SELECT last_insert_rowid() as id');
    return result[0]?.id || 0;
  }

  tableExists(tableName: string): boolean {
    const result = this.query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [tableName]
    );
    return result.length > 0;
  }

  getTableInfo(tableName: string): any[] {
    return this.query(`PRAGMA table_info(${tableName})`);
  }

  backup(backupPath: string): void {
    this.db.backup(backupPath);
  }
}
