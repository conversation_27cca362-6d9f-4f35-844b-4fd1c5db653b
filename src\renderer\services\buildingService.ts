import { Building, CreateBuildingRequest, UpdateBuildingRequest, BuildingWithStats } from '../../types/Building';
import { ApiResponse } from '../../types/common';

export class BuildingService {
  // Create a new building
  static async createBuilding(buildingData: CreateBuildingRequest): Promise<ApiResponse<Building>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Handle file uploads first
      let ownerNidPath: string | undefined;
      let ownerPhotoPath: string | undefined;

      if (buildingData.ownerNid) {
        const nidBuffer = await this.fileToBuffer(buildingData.ownerNid);
        const nidResult = await window.electronAPI.files.upload(
          nidBuffer,
          buildingData.ownerNid.name,
          'building',
          0, // Temporary ID, will be updated after building creation
          'owner_nid'
        );
        ownerNidPath = nidResult.filePath;
      }

      if (buildingData.ownerPhoto) {
        const photoBuffer = await this.fileToBuffer(buildingData.ownerPhoto);
        const photoResult = await window.electronAPI.files.upload(
          photoBuffer,
          buildingData.ownerPhoto.name,
          'building',
          0, // Temporary ID, will be updated after building creation
          'owner_photo'
        );
        ownerPhotoPath = photoResult.filePath;
      }

      // Create building record
      const buildingRecord = {
        name: buildingData.name,
        address: buildingData.address,
        ownerName: buildingData.ownerName,
        ownerMobileNo: buildingData.ownerMobileNo,
        ownerAddress: buildingData.ownerAddress,
        rentAmount: buildingData.rentAmount,
        advance: buildingData.advance,
        numberOfFloor: buildingData.numberOfFloor,
        agreementDate: buildingData.agreementDate.toISOString(),
        handOverFromOwner: buildingData.handOverFromOwner.toISOString(),
        handOverToOwner: buildingData.handOverToOwner?.toISOString(),
        conditions: buildingData.conditions,
        ownerNidPath,
        ownerPhotoPath,
        comments: buildingData.comments,
      };

      const result = await window.electronAPI.buildings.create(buildingRecord);
      
      if (result.success) {
        // Auto-generate floors for the building
        await this.autoGenerateFloors(result.data.id, buildingData.numberOfFloor);
      }

      return result;
    } catch (error) {
      console.error('Error creating building:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create building',
      };
    }
  }

  // Get all buildings
  static async getAllBuildings(): Promise<ApiResponse<Building[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.buildings.getAll();

      if (result.success && result.data) {
        // Transform database results to match our types
        const buildings = result.data.map((building: any) => ({
          id: building.id,
          name: building.name,
          address: building.address,
          ownerName: building.owner_name,
          ownerMobileNo: building.owner_mobile_no,
          ownerAddress: building.owner_address,
          rentAmount: building.rent_amount,
          advance: building.advance,
          numberOfFloor: building.number_of_floor,
          agreementDate: new Date(building.agreement_date),
          handOverFromOwner: new Date(building.hand_over_from_owner),
          handOverToOwner: building.hand_over_to_owner ? new Date(building.hand_over_to_owner) : undefined,
          conditions: building.conditions,
          comments: building.comments,
          createdAt: new Date(building.created_at),
          updatedAt: new Date(building.updated_at),
        }));

        return { success: true, data: buildings };
      }

      return result;
    } catch (error) {
      console.error('Error fetching buildings:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch buildings',
      };
    }
  }

  // Get building by ID
  static async getBuildingById(id: number): Promise<ApiResponse<Building>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      return await window.electronAPI.buildings.getById(id);
    } catch (error) {
      console.error('Error fetching building:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch building',
      };
    }
  }

  // Get buildings with statistics
  static async getBuildingsWithStats(): Promise<ApiResponse<BuildingWithStats[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      return await window.electronAPI.buildings.getWithStats();
    } catch (error) {
      console.error('Error fetching buildings with stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch buildings with statistics',
      };
    }
  }

  // Update building
  static async updateBuilding(buildingData: UpdateBuildingRequest): Promise<ApiResponse<Building>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Handle file uploads if new files are provided
      let ownerNidPath: string | undefined;
      let ownerPhotoPath: string | undefined;

      if (buildingData.ownerNid) {
        const nidBuffer = await this.fileToBuffer(buildingData.ownerNid);
        const nidResult = await window.electronAPI.files.upload(
          nidBuffer,
          buildingData.ownerNid.name,
          'building',
          buildingData.id,
          'owner_nid'
        );
        ownerNidPath = nidResult.filePath;
      }

      if (buildingData.ownerPhoto) {
        const photoBuffer = await this.fileToBuffer(buildingData.ownerPhoto);
        const photoResult = await window.electronAPI.files.upload(
          photoBuffer,
          buildingData.ownerPhoto.name,
          'building',
          buildingData.id,
          'owner_photo'
        );
        ownerPhotoPath = photoResult.filePath;
      }

      const updateData = {
        ...buildingData,
        agreementDate: buildingData.agreementDate?.toISOString(),
        handOverFromOwner: buildingData.handOverFromOwner?.toISOString(),
        handOverToOwner: buildingData.handOverToOwner?.toISOString(),
        ownerNidPath,
        ownerPhotoPath,
      };

      return await window.electronAPI.buildings.update(updateData);
    } catch (error) {
      console.error('Error updating building:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update building',
      };
    }
  }

  // Delete building
  static async deleteBuilding(id: number): Promise<ApiResponse<void>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      return await window.electronAPI.buildings.delete(id);
    } catch (error) {
      console.error('Error deleting building:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete building',
      };
    }
  }

  // Auto-generate floors for a building
  private static async autoGenerateFloors(buildingId: number, numberOfFloors: number): Promise<void> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      await window.electronAPI.floors.autoGenerate(buildingId, numberOfFloors);
    } catch (error) {
      console.error('Error auto-generating floors:', error);
      // Don't throw here as building creation was successful
    }
  }

  // Helper function to convert File to Buffer
  private static fileToBuffer(file: File): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(Buffer.from(reader.result));
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
  }

  // Generate floor name based on floor number
  static generateFloorName(floorNumber: number): string {
    if (floorNumber === 0) return 'Ground Floor';
    if (floorNumber === 1) return '1st Floor';
    if (floorNumber === 2) return '2nd Floor';
    if (floorNumber === 3) return '3rd Floor';
    return `${floorNumber}th Floor`;
  }
}
